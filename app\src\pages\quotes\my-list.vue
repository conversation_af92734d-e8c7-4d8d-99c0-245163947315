<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "报价管理",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import {
  getMyQuotationList,
  publishQuotation,
  toggleQuotationStatus,
  deleteQuotation
} from '@/api/quotation'
import type { 
  IQuotationResponse,
  IMyQuotationListRequest,
  QuotationStatus,
  QUOTATION_STATUS_CONFIG
} from '@/types/quotation'
import { navigateToPage } from '@/utils'

defineOptions({
  name: 'MyQuotationList'
})

// Store
const userStore = useUserStore()

// 页面状态
const isLoading = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)

// 筛选状态
const activeFilter = ref<'all' | 'online' | 'draft'>('all')
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '上线', value: 'online' },
  { label: '草稿', value: 'draft' }
]

// 列表数据
const quotationList = ref<IQuotationResponse[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 状态配置
const statusConfig = {
  Draft: {
    label: '草稿',
    color: '#909399',
    bgColor: '#f4f4f5',
    description: '报价草稿，未公开发布'
  },
  Active: {
    label: '上线',
    color: '#67C23A',
    bgColor: '#f0f9ff',
    description: '报价已公开，可被其他用户查看'
  }
}

// 计算属性
const filteredList = computed(() => {
  if (activeFilter.value === 'all') {
    return quotationList.value
  }
  return quotationList.value // API已经根据filter参数返回过滤后的数据
})

const onlineCount = computed(() => {
  return quotationList.value.filter(q => q.status === 'Active').length
})

const draftCount = computed(() => {
  return quotationList.value.filter(q => q.status === 'Draft').length
})

// 生命周期
onMounted(() => {
  loadQuotationList()
})

// 页面加载
onLoad((options) => {
  // 页面初始化逻辑（如果需要的话）
})

// 加载报价列表
async function loadQuotationList(refresh = false) {
  if (refresh) {
    currentPage.value = 1
    quotationList.value = []
    isRefreshing.value = true
  } else {
    isLoading.value = true
  }

  try {
    const params: IMyQuotationListRequest = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 根据筛选条件设置filter参数
    if (activeFilter.value === 'online') {
      params.filter = 'valid'
    } else if (activeFilter.value === 'draft') {
      params.filter = 'invalid'
    }

    const res = await getMyQuotationList(params)
    const { list, total: totalCount } = res.data

    if (refresh) {
      quotationList.value = list
    } else {
      quotationList.value.push(...list)
    }

    total.value = totalCount
    hasMore.value = quotationList.value.length < totalCount

  } catch (error) {
    console.error('加载报价列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 加载更多
async function loadMore() {
  if (!hasMore.value || isLoading.value) return
  
  currentPage.value++
  await loadQuotationList()
}

// 下拉刷新
async function onRefresh() {
  await loadQuotationList(true)
}

// 下拉刷新恢复
function onRefreshRestore() {
  isRefreshing.value = false
}

// 筛选改变
async function onFilterChange(filter: 'all' | 'online' | 'draft') {
  activeFilter.value = filter
  await loadQuotationList(true)
}

// 创建新报价
function createQuotation() {
  navigateToPage({
    url: '/pages/quotes/edit'
  })
}

// 查看公开报价列表
function viewPublicList() {
  const { ID } = userStore.userInfo
  navigateToPage({
    url: `/pages/quotes/public-list?id=${ID}`
  })
}



// 编辑报价
function editQuotation(quotation: IQuotationResponse) {
  navigateToPage({
    url: `/pages/quotes/edit?id=${quotation.id}`
  })
}

// 查看详情
function viewDetail(quotation: IQuotationResponse) {
  navigateToPage({
    url: `/pages/quotes/detail?id=${quotation.id}&from=mylist`
  })
}

// 发布报价
async function publishQuotationItem(quotation: IQuotationResponse) {
  try {
    uni.showLoading({ title: '发布中...' })

    // 发布报价需要设置过期时间，这里使用默认的7天后过期
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)

    await publishQuotation({
      id: quotation.id,
      expiresAt: expiresAt.toISOString()
    })

    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })

    // 刷新列表
    await loadQuotationList(true)

  } catch (error) {
    console.error('发布报价失败:', error)
    uni.showToast({
      title: '发布失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 切换报价状态（激活 <-> 草稿）
async function toggleQuotationStatusItem(quotation: IQuotationResponse) {
  const isActive = quotation.status === 'Active'
  const actionText = isActive ? '设为草稿' : '激活报价'
  const confirmText = isActive ? '确定要将此报价设为草稿吗？设为草稿后将不再对外展示。' : '确定要激活此报价吗？激活后将对外展示。'

  const res = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: `确认${actionText}`,
      content: confirmText,
      success: (modalRes) => {
        resolve(modalRes.confirm)
      }
    })
  })

  if (!res) return

  try {
    uni.showLoading({ title: `${actionText}中...` })

    await toggleQuotationStatus(quotation.id)

    uni.showToast({
      title: `${actionText}成功`,
      icon: 'success'
    })

    // 刷新列表
    await loadQuotationList(true)

  } catch (error) {
    console.error('切换状态失败:', error)
    uni.showToast({
      title: `${actionText}失败`,
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 格式化价格显示
function formatPrice(quotation: IQuotationResponse): string {
  if (quotation.priceType === 'Fixed') {
    return `¥ ${quotation.price.toFixed(2)}`
  } else if (quotation.priceType === 'Basis' && quotation.instrumentRef) {
    if (quotation.price >= 0) {
      return `${quotation.instrumentRef.instrument_id} + ${quotation.price}`
    } else {
      return `${quotation.instrumentRef.instrument_id} ${quotation.price}`
    }
  } else {
    return quotation.price.toString()
  }
}

// 删除报价
async function deleteQuotationItem(quotation: IQuotationResponse) {
  const res = await new Promise<boolean>((resolve) => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这个报价草稿吗？删除后无法恢复。',
      success: (modalRes) => {
        resolve(modalRes.confirm)
      }
    })
  })

  if (!res) return

  try {
    uni.showLoading({ title: '删除中...' })
    
    await deleteQuotation(quotation.id)
    
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
    
    // 刷新列表
    await loadQuotationList(true)
    
  } catch (error) {
    console.error('删除报价失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    })
  } finally {
    uni.hideLoading()
  }
}

// 格式化剩余时间
function formatRemainingTime(quotation: IQuotationResponse): string {
  if (quotation.status !== 'Active') return ''
  
  if (quotation.isExpired) {
    return '已过期'
  }
  
  if (quotation.remainingHours <= 0) {
    return '即将过期'
  } else if (quotation.remainingHours < 24) {
    return `剩余 ${quotation.remainingHours} 小时`
  } else {
    const days = Math.floor(quotation.remainingHours / 24)
    return `剩余 ${days} 天`
  }
}

// 获取操作按钮
function getActionButtons(quotation: IQuotationResponse) {
  const buttons = []
  
  if (quotation.status === 'Draft') {
    buttons.push(
      { text: '编辑', type: 'primary', action: () => editQuotation(quotation) },
      { text: '发布', type: 'success', action: () => publishQuotationItem(quotation) },
      { text: '删除', type: 'danger', action: () => deleteQuotationItem(quotation) }
    )
  } else if (quotation.status === 'Active') {
    buttons.push(
      { text: '编辑', type: 'primary', action: () => editQuotation(quotation) },
      { text: '查看', type: 'info', action: () => viewDetail(quotation) },
      { text: '设为草稿', type: 'warning', action: () => toggleQuotationStatusItem(quotation) }
    )
  } else {
    buttons.push(
      { text: '查看', type: 'info', action: () => viewDetail(quotation) }
    )
  }
  
  return buttons
}
</script>

<template>
  <view class="app-container">
    <view class="app-content">
      <!-- 顶部操作栏 -->
      <view class="header-section">
        <view class="header-content">
          <!-- 筛选标签 -->
          <view class="filter-tabs">
            <view
              v-for="option in filterOptions"
              :key="option.value"
              class="filter-tab"
              :class="{ active: activeFilter === option.value }"
              @click="onFilterChange(option.value as any)"
            >
              {{ option.label }}
            </view>
          </view>

          <!-- 右侧操作按钮 -->
          <view class="header-actions">
            <wd-button type="icon" icon="add" custom-class="add-btn" @click="createQuotation" custom-style="color: var(--app-color-primary); border-color: var(--app-color-primary);" />
            <wd-button type="icon" icon="view" custom-class="view-btn" @click="viewPublicList" custom-style="color: var(--app-color-primary); border-color: var(--app-color-primary);" />
          </view>
        </view>
      </view>

    <!-- 列表内容 -->
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      refresher-background="#f5f5f5"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRefreshRestore"
      @scrolltolower="loadMore"
    >
      <view class="list-container">
        <!-- 报价列表 -->
        <view v-if="quotationList.length > 0" class="quotation-list">
          <view
            v-for="quotation in filteredList"
            :key="quotation.id"
            class="app-card quotation-card"
            @click="viewDetail(quotation)"
          >
            <!-- 右上角标签组 -->
            <view class="tag-group">
              <!-- 状态标签 -->
              <view
                class="status-tag"
                :style="{
                  color: statusConfig[quotation.status].color,
                  backgroundColor: statusConfig[quotation.status].bgColor
                }"
              >
                {{ statusConfig[quotation.status].label }}
              </view>

              <!-- 报价类型标签 -->
              <view class="quote-type-tag">
                {{ quotation.isBuyRequest ? '求购' : '出售' }}
              </view>

              <!-- 价格类型标签 -->
              <view class="price-type-tag">
                {{ quotation.priceType === 'Fixed' ? '一口价' : quotation.priceType === 'Basis' ? '基差价' : '商议' }}
              </view>
            </view>

            <!-- 主要信息 -->
            <view class="card-header">
              <text class="quotation-title">{{ quotation.title }}</text>
            </view>

            <!-- 价格信息 - 突出显示 -->
            <view class="price-section">
              <text class="quotation-price">{{ formatPrice(quotation) }}</text>
            </view>
            
            <!-- 次要信息 -->
            <view class="card-content">
              <view class="info-row">
                <text class="label">商品：</text>
                <text class="value">{{ quotation.commodityName || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="label">地点：</text>
                <text class="value">{{ quotation.deliveryLocation }}</text>
              </view>
              <view v-if="quotation.brand" class="info-row">
                <text class="label">品牌：</text>
                <text class="value">{{ quotation.brand }}</text>
              </view>
            </view>
            
            <!-- 时间信息 -->
            <view class="card-footer">
              <text class="create-time">{{ quotation.createdAt.split('T')[0] }}</text>
              <text 
                v-if="quotation.status === 'Active'"
                class="remaining-time"
                :class="{ expired: quotation.isExpired }"
              >
                {{ formatRemainingTime(quotation) }}
              </text>
            </view>
            
            <!-- 操作按钮 -->
            <view class="action-buttons" @click.stop>
              <wd-button
                v-for="button in getActionButtons(quotation)"
                :key="button.text"
                :type="button.type"
                size="small"
                @click="button.action"
              >
                {{ button.text }}
              </wd-button>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-else-if="!isLoading" class="empty-state">
          <text class="empty-text">暂无报价</text>
          <wd-button type="primary" @click="createQuotation">
            创建第一个报价
          </wd-button>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="isLoading && quotationList.length > 0" class="loading-more">
          <wd-loading size="24rpx" />
          <text class="loading-text">加载中...</text>
        </view>
        
        <!-- 没有更多 -->
        <view v-if="!hasMore && quotationList.length > 0" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.header-section {
  background: var(--app-bg-primary);
  box-shadow: var(--app-shadow-sm);
  border-radius: 0 0 var(--app-radius-md) var(--app-radius-md);
  margin-bottom: var(--app-spacing-sm);
}

.header-content {
  display: flex;
  align-items: center;
  padding: var(--app-spacing-sm) var(--app-spacing-base);
}

.filter-tabs {
  display: flex;
  flex: 1;
  margin-right: var(--app-spacing-lg);
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: var(--app-spacing-sm) var(--app-spacing-xs);
  font-size: var(--app-font-size-sm);
  color: var(--app-text-secondary);
  transition: all var(--app-duration-base) var(--app-ease-out);
  position: relative;

  &.active {
    color: var(--app-color-primary);
    font-weight: var(--app-font-weight-medium);
    border-bottom: 4rpx solid var(--app-color-primary);
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);
}


.scroll-container {
  flex: 1;
}

.list-container {
  padding: var(--app-spacing-sm) 0 0;
}

.quotation-list {
  margin: 0 var(--app-spacing-sm);

  .quotation-card {
    padding: var(--app-spacing-base);
    margin-bottom: var(--app-spacing-sm);
    position: relative;
    transition: all var(--app-duration-base) var(--app-ease-out);

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: var(--app-shadow-md);
    }

    // 右上角标签组
    .tag-group {
      position: absolute;
      top: var(--app-spacing-sm);
      right: var(--app-spacing-sm);
      display: flex;
      flex-direction: row;
      gap: var(--app-spacing-xs);
      align-items: flex-end;
    }

    .status-tag,
    .quote-type-tag,
    .price-type-tag {
      padding: var(--app-spacing-xs) var(--app-spacing-sm);
      border-radius: var(--app-radius-full);
      font-size: var(--app-font-size-xs);
      font-weight: var(--app-font-weight-medium);
      white-space: nowrap;
    }

    .quote-type-tag {
      background: linear-gradient(135deg, var(--app-color-info-light), var(--app-color-info));
      color: var(--app-color-info);
      border: 1rpx solid var(--app-color-info);
    }

    .price-type-tag {
      background: linear-gradient(135deg, var(--app-color-secondary-100), var(--app-color-secondary-200));
      color: var(--app-color-secondary-600);
      border: 1rpx solid var(--app-color-secondary-300);
    }

    .card-header {
      margin-bottom: var(--app-spacing-sm);
      padding-right: 200rpx; // 为右侧标签组留空间

      .quotation-title {
        font-size: var(--app-font-size-lg);
        font-weight: var(--app-font-weight-semibold);
        color: var(--app-text-primary);
        line-height: var(--app-line-height-tight);
        margin: 0;
      }
    }

    // 价格区域 - 突出显示
    .price-section {
      margin-bottom: var(--app-spacing-base);
      padding: var(--app-spacing-sm) var(--app-spacing-base);
      background: linear-gradient(135deg, var(--app-color-primary-50), var(--app-color-primary-100));
      border-radius: var(--app-radius-md);
      border-left: 4rpx solid var(--app-color-primary);

      .quotation-price {
        font-size: var(--app-font-size-xl);
        font-weight: var(--app-font-weight-bold);
        color: var(--app-color-primary);
        display: block;
        text-align: center;
      }
    }

    .card-content {
      margin-bottom: var(--app-spacing-sm);

      .info-row {
        display: flex;
        margin-bottom: var(--app-spacing-xs);
        font-size: var(--app-font-size-sm);

        .label {
          color: var(--app-text-secondary);
          width: 200rpx;
          flex-shrink: 0;
        }

        .value {
          color: var(--app-text-primary);
          flex: 1;
        }
      }
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--app-spacing-sm);
      font-size: var(--app-font-size-xs);

      .create-time {
        color: var(--app-text-tertiary);
      }

      .remaining-time {
        color: var(--app-color-success);
        font-weight: var(--app-font-weight-medium);

        &.expired {
          color: var(--app-color-error);
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: var(--app-spacing-sm);
      justify-content: flex-end;

      :deep(.wd-button) {
        min-width: 120rpx;
        border-radius: var(--app-radius-md);
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-xl) var(--app-spacing-lg);

  .empty-text {
    font-size: var(--app-font-size-base);
    color: var(--app-text-tertiary);
    margin: var(--app-spacing-base) 0 var(--app-spacing-lg);
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-lg);

  .loading-text {
    margin-left: var(--app-spacing-sm);
    font-size: var(--app-font-size-sm);
    color: var(--app-text-tertiary);
  }
}

.no-more {
  text-align: center;
  padding: var(--app-spacing-lg);
  font-size: var(--app-font-size-sm);
  color: var(--app-text-tertiary);
}
</style>
