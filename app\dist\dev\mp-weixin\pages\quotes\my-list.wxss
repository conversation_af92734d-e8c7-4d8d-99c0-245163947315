/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.header-section.data-v-f81ce579 {
  background: var(--app-bg-primary);
  box-shadow: var(--app-shadow-sm);
  border-radius: 0 0 var(--app-radius-md) var(--app-radius-md);
  margin-bottom: var(--app-spacing-sm);
}
.header-content.data-v-f81ce579 {
  display: flex;
  align-items: center;
  padding: var(--app-spacing-sm) var(--app-spacing-base);
}
.filter-tabs.data-v-f81ce579 {
  display: flex;
  flex: 1;
  margin-right: var(--app-spacing-lg);
}
.filter-tab.data-v-f81ce579 {
  flex: 1;
  text-align: center;
  padding: var(--app-spacing-sm) var(--app-spacing-xs);
  font-size: var(--app-font-size-sm);
  color: var(--app-text-secondary);
  transition: all var(--app-duration-base) var(--app-ease-out);
  position: relative;
}
.filter-tab.active.data-v-f81ce579 {
  color: var(--app-color-primary);
  font-weight: var(--app-font-weight-medium);
  border-bottom: 4rpx solid var(--app-color-primary);
}
.header-actions.data-v-f81ce579 {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);
}
.scroll-container.data-v-f81ce579 {
  flex: 1;
}
.list-container.data-v-f81ce579 {
  padding: var(--app-spacing-sm) 0 0;
}
.quotation-list.data-v-f81ce579 {
  margin: 0 var(--app-spacing-sm);
}
.quotation-list .quotation-card.data-v-f81ce579 {
  padding: var(--app-spacing-base);
  margin-bottom: var(--app-spacing-sm);
  position: relative;
  transition: all var(--app-duration-base) var(--app-ease-out);
}
.quotation-list .quotation-card.data-v-f81ce579:hover {
  transform: translateY(-2rpx);
  box-shadow: var(--app-shadow-md);
}
.quotation-list .quotation-card .tag-group.data-v-f81ce579 {
  position: absolute;
  top: var(--app-spacing-sm);
  right: var(--app-spacing-sm);
  display: flex;
  flex-direction: row;
  gap: var(--app-spacing-xs);
  align-items: flex-end;
}
.quotation-list .quotation-card .status-tag.data-v-f81ce579,
.quotation-list .quotation-card .quote-type-tag.data-v-f81ce579,
.quotation-list .quotation-card .price-type-tag.data-v-f81ce579 {
  padding: var(--app-spacing-xs) var(--app-spacing-sm);
  border-radius: var(--app-radius-full);
  font-size: var(--app-font-size-xs);
  font-weight: var(--app-font-weight-medium);
  white-space: nowrap;
}
.quotation-list .quotation-card .quote-type-tag.data-v-f81ce579 {
  background: linear-gradient(135deg, var(--app-color-info-light), var(--app-color-info));
  color: var(--app-color-info);
  border: 1rpx solid var(--app-color-info);
}
.quotation-list .quotation-card .price-type-tag.data-v-f81ce579 {
  background: linear-gradient(135deg, var(--app-color-secondary-100), var(--app-color-secondary-200));
  color: var(--app-color-secondary-600);
  border: 1rpx solid var(--app-color-secondary-300);
}
.quotation-list .quotation-card .card-header.data-v-f81ce579 {
  margin-bottom: var(--app-spacing-sm);
  padding-right: 200rpx;
}
.quotation-list .quotation-card .card-header .quotation-title.data-v-f81ce579 {
  font-size: var(--app-font-size-lg);
  font-weight: var(--app-font-weight-semibold);
  color: var(--app-text-primary);
  line-height: var(--app-line-height-tight);
  margin: 0;
}
.quotation-list .quotation-card .price-section.data-v-f81ce579 {
  margin-bottom: var(--app-spacing-base);
  padding: var(--app-spacing-sm) var(--app-spacing-base);
  background: linear-gradient(135deg, var(--app-color-primary-50), var(--app-color-primary-100));
  border-radius: var(--app-radius-md);
  border-left: 4rpx solid var(--app-color-primary);
}
.quotation-list .quotation-card .price-section .quotation-price.data-v-f81ce579 {
  font-size: var(--app-font-size-xl);
  font-weight: var(--app-font-weight-bold);
  color: var(--app-color-primary);
  display: block;
  text-align: center;
}
.quotation-list .quotation-card .card-content.data-v-f81ce579 {
  margin-bottom: var(--app-spacing-sm);
}
.quotation-list .quotation-card .card-content .info-row.data-v-f81ce579 {
  display: flex;
  margin-bottom: var(--app-spacing-xs);
  font-size: var(--app-font-size-sm);
}
.quotation-list .quotation-card .card-content .info-row .label.data-v-f81ce579 {
  color: var(--app-text-secondary);
  width: 200rpx;
  flex-shrink: 0;
}
.quotation-list .quotation-card .card-content .info-row .value.data-v-f81ce579 {
  color: var(--app-text-primary);
  flex: 1;
}
.quotation-list .quotation-card .card-footer.data-v-f81ce579 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--app-spacing-sm);
  font-size: var(--app-font-size-xs);
}
.quotation-list .quotation-card .card-footer .create-time.data-v-f81ce579 {
  color: var(--app-text-tertiary);
}
.quotation-list .quotation-card .card-footer .remaining-time.data-v-f81ce579 {
  color: var(--app-color-success);
  font-weight: var(--app-font-weight-medium);
}
.quotation-list .quotation-card .card-footer .remaining-time.expired.data-v-f81ce579 {
  color: var(--app-color-error);
}
.quotation-list .quotation-card .action-buttons.data-v-f81ce579 {
  display: flex;
  gap: var(--app-spacing-sm);
  justify-content: flex-end;
}
.quotation-list .quotation-card .action-buttons.data-v-f81ce579 .wd-button {
  min-width: 120rpx;
  border-radius: var(--app-radius-md);
}
.empty-state.data-v-f81ce579 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-xl) var(--app-spacing-lg);
}
.empty-state .empty-text.data-v-f81ce579 {
  font-size: var(--app-font-size-base);
  color: var(--app-text-tertiary);
  margin: var(--app-spacing-base) 0 var(--app-spacing-lg);
}
.loading-more.data-v-f81ce579 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-lg);
}
.loading-more .loading-text.data-v-f81ce579 {
  margin-left: var(--app-spacing-sm);
  font-size: var(--app-font-size-sm);
  color: var(--app-text-tertiary);
}
.no-more.data-v-f81ce579 {
  text-align: center;
  padding: var(--app-spacing-lg);
  font-size: var(--app-font-size-sm);
  color: var(--app-text-tertiary);
}